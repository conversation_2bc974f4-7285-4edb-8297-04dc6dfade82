using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using EAMS.API.DTOs;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using AutoMapper;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class OrganisationsController : ControllerBase
{
    private readonly IOrganisationService _organisationService;
    private readonly ILogger<OrganisationsController> _logger;
    private readonly IMapper _mapper;

    public OrganisationsController(
        IOrganisationService organisationService,
        ILogger<OrganisationsController> logger,
        IMapper mapper)
    {
        _organisationService = organisationService;
        _logger = logger;
        _mapper = mapper;
    }

    /// <summary>
    /// Get all organisations
    /// </summary>
    [HttpGet]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Read")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<IEnumerable<OrganisationDto>>> GetOrganisations()
    {
        try
        {
            var organisations = await _organisationService.GetAllOrganisationsAsync();
            var response = _mapper.Map<IEnumerable<OrganisationDto>>(organisations);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving organisations");
            return StatusCode(500, "An error occurred while retrieving organisations");
        }
    }

    /// <summary>
    /// Get organisation by ID
    /// </summary>
    [HttpGet("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Read")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<OrganisationDto>> GetOrganisation(Guid id)
    {
        try
        {
            var organisation = await _organisationService.GetOrganisationByIdAsync(id);

            if (organisation == null)
            {
                return NotFound($"Organisation with ID {id} not found");
            }

            var response = _mapper.Map<OrganisationDto>(organisation);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving organisation with ID {Id}", id);
            return StatusCode(500, "An error occurred while retrieving the organisation");
        }
    }

    /// <summary>
    /// Create a new organisation
    /// </summary>
    [HttpPost]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<OrganisationDto>> CreateOrganisation(OrganisationDto organisationDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var organisation = _mapper.Map<Organisation>(organisationDto);
            var createdOrganisation = await _organisationService.CreateOrganisationAsync(organisation);
            var response = _mapper.Map<OrganisationDto>(createdOrganisation);

            return CreatedAtAction(nameof(GetOrganisation), new { id = createdOrganisation.Id }, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating organisation");
            return StatusCode(500, "An error occurred while creating the organisation");
        }
    }

    /// <summary>
    /// Update an existing organisation
    /// </summary>
    [HttpPut("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<OrganisationDto>> UpdateOrganisation(Guid id, OrganisationDto organisationDto)
    {
        try
        {
            if (id != organisationDto.Id)
                return BadRequest("Organisation ID mismatch.");

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var organisation = _mapper.Map<Organisation>(organisationDto);
            var updatedOrganisation = await _organisationService.UpdateOrganisationAsync(organisation);
            var response = _mapper.Map<OrganisationDto>(updatedOrganisation);

            return Ok(response);
        }
        catch (InvalidOperationException ex)
        {
            if (ex.Message != null && ex.Message.Contains("not found", StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning(ex, "Organisation with ID {Id} not found for update", id);
                return NotFound(ex.Message);
            }
            else
            {
                _logger.LogWarning(ex, "Unexpected InvalidOperationException when updating accommodation with ID {id}", id);
                return StatusCode(500, "An error occured while updating the accommodation");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating organisation with ID {Id}", id);
            return StatusCode(500, "An error occurred while updating the organisation");
        }
    }

    /// <summary>
    /// Delete an organisation
    /// </summary>
    [HttpDelete("{id}")]
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Write")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<bool>> DeleteOrganisation(Guid id)
    {
        try
        {
            var exists = await _organisationService.OrganisationExistsAsync(id);
            if (!exists)
            {
                return NotFound(false);
            }

            var result = await _organisationService.DeleteOrganisationAsync(id);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting organisation with ID {Id}", id);
            return StatusCode(500, "An error occurred while deleting the organisation");
        }
    }
}
